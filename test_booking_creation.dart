// This file is moved to lib/utils/booking_test_helper.dart

class BookingTestHelper {
  static final _client = Supabase.instance.client;

  /// Create a test booking directly in the database
  static Future<void> createTestBooking() async {
    try {
      print('🧪 Creating test booking...');

      // Get current user (driver)
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        print('❌ No authenticated user');
        return;
      }

      final driverId = currentUser.id;
      print('👤 Driver ID: $driverId');

      // Get driver's first trip
      final tripsResponse = await _client
          .from('trips')
          .select('id, title, from_city, to_city, price')
          .eq('leader_id', driverId)
          .limit(1);

      if (tripsResponse.isEmpty) {
        print('❌ No trips found for driver');
        return;
      }

      final trip = tripsResponse.first;
      final tripId = trip['id'] as String;
      print('🚗 Using trip: ${trip['title']} (${trip['id']})');

      // Create test passenger
      final testPassengerId = 'test-passenger-${DateTime.now().millisecondsSinceEpoch}';
      
      await _client.from('users').upsert({
        'id': testPassengerId,
        'email': '<EMAIL>',
        'full_name': 'فاطمة الزهراء',
        'phone': '+212612345678',
        'role': 'passenger',
        'city': 'الرباط',
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      print('👥 Test passenger created: $testPassengerId');

      // Create test booking
      final testBookingId = 'test-booking-${DateTime.now().millisecondsSinceEpoch}';
      
      final bookingData = {
        'id': testBookingId,
        'trip_id': tripId,
        'passenger_id': testPassengerId,
        'driver_id': driverId,
        'seats_booked': 1,
        'total_price': (trip['price'] as num).toDouble(),
        'booking_type': 'manual',
        'status': 'pending',
        'message': 'أتطلع للرحلة معكم، أرجو قبول طلب الحجز',
        'special_requests': 'مقعد بجانب النافذة إذا أمكن',
        'passenger_details': {
          'passengers': [
            {
              'name': 'فاطمة الزهراء',
              'phone': '+212612345678',
              'age': 28,
            }
          ]
        },
        'is_paid': false,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      await _client.from('bookings').insert(bookingData);

      print('✅ Test booking created successfully: $testBookingId');
      print('   Trip: ${trip['from_city']} → ${trip['to_city']}');
      print('   Passenger: فاطمة الزهراء');
      print('   Status: pending');
      print('   Price: ${trip['price']} درهم');

    } catch (e) {
      print('❌ Error creating test booking: $e');
    }
  }

  /// Check existing bookings for a driver
  static Future<void> checkDriverBookings(String driverId) async {
    try {
      print('🔍 Checking bookings for driver: $driverId');

      final response = await _client
          .from('bookings')
          .select('''
            id,
            status,
            seats_booked,
            total_price,
            created_at,
            trip:trips(title, from_city, to_city),
            passenger:users!passenger_id(full_name)
          ''')
          .eq('driver_id', driverId)
          .order('created_at', ascending: false);

      print('📊 Found ${response.length} bookings:');
      
      for (final booking in response) {
        print('   • ${booking['id']}');
        print('     Status: ${booking['status']}');
        print('     Passenger: ${booking['passenger']?['full_name'] ?? 'Unknown'}');
        print('     Trip: ${booking['trip']?['from_city']} → ${booking['trip']?['to_city']}');
        print('     Price: ${booking['total_price']} درهم');
        print('     Created: ${booking['created_at']}');
        print('');
      }

    } catch (e) {
      print('❌ Error checking bookings: $e');
    }
  }

  /// Test the booking query methods
  static Future<void> testBookingQueries(String driverId) async {
    try {
      print('🧪 Testing booking query methods...');

      // Test direct query
      final directResponse = await _client
          .from('bookings')
          .select('*')
          .eq('driver_id', driverId);

      print('📊 Direct query result: ${directResponse.length} bookings');

      // Test function call
      try {
        final functionResponse = await _client
            .rpc('get_driver_bookings', params: {
              'p_driver_id': driverId,
              'p_status': null,
            });

        print('📊 Function query result: ${functionResponse?.length ?? 0} bookings');
      } catch (e) {
        print('❌ Function query failed: $e');
      }

    } catch (e) {
      print('❌ Error testing queries: $e');
    }
  }
}
